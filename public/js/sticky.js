/**
 * 处理粘性元素的滚动行为
 */

// 增强的跟随页面滚动功能，解决滚动到底部的问题和布局混乱问题
document.addEventListener('DOMContentLoaded', function() {
  // 处理事件容器（index.html页面）
  handleStickyElement('events-container', 'events-sticky', 'events-placeholder');

  // 处理article页面的活动日历容器
  handleStickyElement('article-events-container', 'article-events-sticky', 'article-events-placeholder');

  // 处理category页面的活动日历容器
  handleStickyElement('category-events-container', 'category-events-sticky', 'category-events-placeholder');

  // 处理右侧边栏元素
  handleRightSidebarSticky();

  // 处理main-right元素
  handleMainRightSticky();
  
  function handleStickyElement(containerId, elementId, placeholderId) {
    const container = document.getElementById(containerId);
    const stickyElement = document.getElementById(elementId);
    const placeholder = document.getElementById(placeholderId);

    if (!container || !stickyElement || !placeholder) return;
    
    // 设置占位符的初始高度和宽度，与粘性元素相同
    placeholder.style.height = stickyElement.offsetHeight + 'px';
    placeholder.style.width = stickyElement.offsetWidth + 'px';
    
    // 计算容器的位置信息
    let containerRect = container.getBoundingClientRect();
    let containerTop = containerRect.top + window.pageYOffset;
    let containerBottom = containerRect.bottom + window.pageYOffset;
    let containerHeight = containerRect.height;
    let stickyHeight = stickyElement.offsetHeight;
    
    // 监听窗口大小变化，更新位置信息
    window.addEventListener('resize', function() {
      containerRect = container.getBoundingClientRect();
      containerTop = containerRect.top + window.pageYOffset;
      containerBottom = containerRect.bottom + window.pageYOffset;
      containerHeight = containerRect.height;
      stickyHeight = stickyElement.offsetHeight;
      
      // 更新占位符尺寸
      placeholder.style.height = stickyElement.offsetHeight + 'px';
      placeholder.style.width = stickyElement.offsetWidth + 'px';
      
      // 触发滚动事件以更新位置
      window.dispatchEvent(new Event('scroll'));
    });
    
    // 监听滚动事件
    window.addEventListener('scroll', function() {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const viewportHeight = window.innerHeight;


      
      // 计算元素应该处于的状态
      if (scrollTop < containerTop - 20) {
        // 1. 还没滚动到容器顶部，保持正常流
        stickyElement.style.position = '';  // 清除position属性，使用CSS默认值
        stickyElement.style.top = '';
        stickyElement.style.bottom = '';
        stickyElement.style.zIndex = '';  // 清除z-index，使用CSS默认值
        placeholder.classList.remove('show-placeholder');
      }
      else if (scrollTop + stickyHeight + 20 >= containerBottom) {
        // 2. 滚动到容器底部，固定在容器底部
        stickyElement.style.position = 'absolute';
        stickyElement.style.top = '0';  // 设置为0，因为已经在CSS中设置了margin-top: 20px
        stickyElement.style.bottom = '';
        stickyElement.style.zIndex = '100';  // 确保不会覆盖弹出框
        placeholder.classList.add('show-placeholder');
      }
      else {
        // 3. 在容器中间滚动，固定在视口顶部
        stickyElement.style.position = 'fixed';
        stickyElement.style.top = '20px';  // 保持20px的顶部间距
        stickyElement.style.bottom = '';
        stickyElement.style.zIndex = '100';  // 确保不会覆盖弹出框
        placeholder.classList.add('show-placeholder');
      }
    });
    
    // 初始触发一次滚动事件
    window.dispatchEvent(new Event('scroll'));
  }

  // 处理右侧边栏粘性元素的函数
  function handleRightSidebarSticky() {
    // 获取sidebar中的元素（立即跟随滚动）
    const float1 = document.querySelector('.sidebar .float1');
    const float2 = document.querySelector('.sidebar .float2');

    // 获取news-ad元素（延迟跟随滚动）
    const newsAds = document.querySelectorAll('.news-ad');

    // 创建sidebar元素数组（立即跟随）
    const sidebarElements = [];

    if (float1) {
      console.log('找到 float1 元素');
      sidebarElements.push({
        element: float1,
        originalParent: float1.parentNode,
        originalNextSibling: float1.nextSibling,
        type: 'sidebar'
      });
    }

    if (float2) {
      console.log('找到 float2 元素');
      sidebarElements.push({
        element: float2,
        originalParent: float2.parentNode,
        originalNextSibling: float2.nextSibling,
        type: 'sidebar'
      });
    }

    // 创建news-ad元素数组（延迟跟随）
    const newsAdElements = [];
    newsAds.forEach(function(newsAd) {
      newsAdElements.push({
        element: newsAd,
        originalParent: newsAd.parentNode,
        originalNextSibling: newsAd.nextSibling,
        type: 'news-ad'
      });
    });

    // 合并所有元素
    const allElements = [...sidebarElements, ...newsAdElements];

    if (allElements.length === 0) return;

    // 创建粘性容器
    const stickyContainer = document.createElement('div');
    stickyContainer.className = 'right-sidebar-sticky-container';

    // 计算正确的右侧位置
    function updateStickyPosition() {
      const bodyWidth = 1240; // 页面主体宽度
      const viewportWidth = window.innerWidth;
      // 计算页面居中后的右边距，保持与原始位置一致（40px）
      const rightOffset = Math.max(40, (viewportWidth - bodyWidth) / 2 + 40);

      stickyContainer.style.cssText = `
        position: fixed;
        top: 20px;
        right: ${rightOffset}px;
        width: 260px;
        z-index: 99;
        display: none;
        pointer-events: auto;
      `;
    }

    updateStickyPosition();

    // 将粘性容器添加到body
    document.body.appendChild(stickyContainer);

    // 为每个元素创建占位符
    allElements.forEach(function(item) {
      const placeholder = document.createElement('div');
      placeholder.className = 'sticky-placeholder-' + item.type;
      placeholder.style.cssText = `
        width: ${item.element.offsetWidth}px;
        height: ${item.element.offsetHeight}px;
        display: none;
      `;

      // 在原位置插入占位符
      item.originalParent.insertBefore(placeholder, item.originalNextSibling);
      item.placeholder = placeholder;
    });

    let isSticky = false;

    // 滚动事件处理
    function handleScroll() {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const viewportHeight = window.innerHeight;
      const sidebar = document.querySelector('.sidebar');
      const footer = document.querySelector('.footer');

      if (!sidebar) return;

      const sidebarRect = sidebar.getBoundingClientRect();
      const sidebarTop = sidebarRect.top + scrollTop;

      // 获取footer位置信息
      let footerTop = Infinity;
      if (footer) {
        const footerRect = footer.getBoundingClientRect();
        footerTop = footerRect.top + scrollTop;
      }

      // 计算粘性容器的高度
      let stickyContainerHeight = 0;
      if (isSticky) {
        stickyContainerHeight = stickyContainer.offsetHeight;
      } else {
        // 预估高度
        allElements.forEach(function(item) {
          stickyContainerHeight += item.element.offsetHeight + 20; // 20px margin
        });
      }

      // 判断是否应该激活粘性效果 - 当sidebar开始滚动出视口时立即激活
      const shouldBeSticky = scrollTop > sidebarTop - 20;

      // 检查是否会与footer重叠
      const stickyBottomPosition = scrollTop + 20 + stickyContainerHeight; // 20px是top值
      const willOverlapFooter = stickyBottomPosition > footerTop;

      if (shouldBeSticky && !isSticky) {
        // 激活粘性效果
        console.log('激活粘性效果，移动元素到粘性容器');
        isSticky = true;
        stickyContainer.style.display = 'block';
        stickyContainer.innerHTML = ''; // 清空容器

        // 处理所有元素，都立即跟随滚动
        allElements.forEach(function(item) {
          // 显示占位符
          item.placeholder.style.display = 'block';

          // 直接移动原始元素到粘性容器（保持轮播功能）
          item.element.style.marginBottom = '20px';
          stickyContainer.appendChild(item.element);
          console.log(`移动 ${item.type} 元素到粘性容器`);
        });

      } else if (!shouldBeSticky && isSticky) {
        // 取消粘性效果（只有当滚动回到顶部时才取消）
        console.log('取消粘性效果，将元素移回原位置');
        isSticky = false;
        stickyContainer.style.display = 'none';

        allElements.forEach(function(item) {
          // 隐藏占位符
          item.placeholder.style.display = 'none';

          // 将元素移回原位置
          if (item.originalNextSibling && item.originalNextSibling.parentNode === item.originalParent) {
            item.originalParent.insertBefore(item.element, item.originalNextSibling);
          } else {
            item.originalParent.appendChild(item.element);
          }

          // 重置margin样式
          item.element.style.marginBottom = '';
          console.log(`将 ${item.type} 元素移回原位置`);
        });
      }

      // 调整粘性容器的位置
      if (isSticky) {
        if (willOverlapFooter) {
          // 如果会与footer重叠，让元素跟随footer往上滚动
          // 计算元素应该相对于页面的绝对位置
          const absoluteTop = footerTop - stickyContainerHeight - 20; // footer上方20px
          stickyContainer.style.position = 'absolute';
          stickyContainer.style.top = absoluteTop + 'px';
        } else {
          // 正常跟随滚动
          stickyContainer.style.position = 'fixed';
          stickyContainer.style.top = '20px';
        }
      }
    }

    // 监听滚动事件
    window.addEventListener('scroll', handleScroll);

    // 监听窗口大小变化
    window.addEventListener('resize', function() {
      // 重新计算粘性容器的位置
      updateStickyPosition();
    });

    // 初始触发一次滚动事件
    handleScroll();
  }

  // 处理main-right元素的跟随滚动功能
  function handleMainRightSticky() {
    // 获取main-right中的元素
    const float1 = document.querySelector('.main-right .float1');
    const float2 = document.querySelector('.main-right .float2');
    const newsAds = document.querySelectorAll('.main-right .news-ad');

    // 创建元素数组
    const allElements = [];

    if (float1) {
      allElements.push({
        element: float1,
        originalParent: float1.parentNode,
        originalNextSibling: float1.nextSibling,
        type: 'float'
      });
    }

    if (float2) {
      allElements.push({
        element: float2,
        originalParent: float2.parentNode,
        originalNextSibling: float2.nextSibling,
        type: 'float'
      });
    }

    newsAds.forEach(function(newsAd) {
      allElements.push({
        element: newsAd,
        originalParent: newsAd.parentNode,
        originalNextSibling: newsAd.nextSibling,
        type: 'news-ad'
      });
    });

    if (allElements.length === 0) return;

    // 创建粘性容器
    const stickyContainer = document.createElement('div');
    stickyContainer.className = 'right-sidebar-sticky-container';

    // 计算正确的右侧位置
    function updateStickyPosition() {
      const bodyWidth = 1240; // 页面主体宽度
      const viewportWidth = window.innerWidth;
      // 计算页面居中后的右边距，保持与原始位置一致（40px）
      const rightOffset = Math.max(40, (viewportWidth - bodyWidth) / 2 + 40);

      stickyContainer.style.cssText = `
        position: fixed;
        top: 20px;
        right: ${rightOffset}px;
        width: 260px;
        z-index: 99;
        display: none;
        pointer-events: auto;
      `;
    }

    // 初始化位置
    updateStickyPosition();

    // 将粘性容器添加到body
    document.body.appendChild(stickyContainer);

    // 创建占位符但不立即移动元素
    const placeholders = allElements.map(function(item) {
      const placeholder = document.createElement('div');
      placeholder.className = `sticky-placeholder-${item.type}`;
      placeholder.style.cssText = `
        width: ${item.element.offsetWidth}px;
        height: ${item.element.offsetHeight}px;
        margin-bottom: 20px;
        visibility: hidden;
        display: none;
      `;

      // 将占位符插入到原始位置
      if (item.originalNextSibling) {
        item.originalParent.insertBefore(placeholder, item.originalNextSibling);
      } else {
        item.originalParent.appendChild(placeholder);
      }

      return {
        placeholder: placeholder,
        element: item.element,
        originalParent: item.originalParent,
        originalNextSibling: item.originalNextSibling
      };
    });

    // 标记是否已经移动到粘性容器
    let isSticky = false;

    // 滚动处理函数
    function handleScroll() {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const viewportHeight = window.innerHeight;
      const footer = document.querySelector('.footer');

      // 获取footer位置信息
      let footerTop = Infinity;
      if (footer) {
        const footerRect = footer.getBoundingClientRect();
        footerTop = footerRect.top + scrollTop;
      }

      // 计算粘性容器的高度
      let stickyContainerHeight = 0;
      if (isSticky) {
        stickyContainerHeight = stickyContainer.offsetHeight;
      } else {
        // 预估高度
        allElements.forEach(function(item) {
          stickyContainerHeight += item.element.offsetHeight + 20; // 20px margin
        });
      }

      // 检查是否会与footer重叠
      const stickyBottomPosition = scrollTop + 20 + stickyContainerHeight; // 20px是top值
      const willOverlapFooter = stickyBottomPosition > footerTop;

      if (scrollTop > 100) { // 滚动超过100px时显示粘性容器
        if (!isSticky) {
          // 移动元素到粘性容器
          allElements.forEach(function(item) {
            stickyContainer.appendChild(item.element);
          });

          // 显示占位符
          placeholders.forEach(function(item) {
            item.placeholder.style.display = 'block';
            item.placeholder.style.visibility = 'visible';
          });

          isSticky = true;
        }
        stickyContainer.style.display = 'block';
      } else {
        if (isSticky) {
          // 将元素移回原位置
          placeholders.forEach(function(item) {
            if (item.originalNextSibling && item.originalNextSibling.parentNode === item.originalParent) {
              item.originalParent.insertBefore(item.element, item.originalNextSibling);
            } else {
              item.originalParent.appendChild(item.element);
            }

            // 隐藏占位符
            item.placeholder.style.display = 'none';
            item.placeholder.style.visibility = 'hidden';
          });

          isSticky = false;
        }
        stickyContainer.style.display = 'none';
      }

      // 调整粘性容器的位置
      if (isSticky) {
        if (willOverlapFooter) {
          // 如果会与footer重叠，让元素跟随footer往上滚动
          // 计算元素应该相对于页面的绝对位置
          const absoluteTop = footerTop - stickyContainerHeight - 20; // footer上方20px
          stickyContainer.style.position = 'absolute';
          stickyContainer.style.top = absoluteTop + 'px';
        } else {
          // 正常跟随滚动
          stickyContainer.style.position = 'fixed';
          stickyContainer.style.top = '20px';
        }
      }
    }

    // 监听滚动事件
    window.addEventListener('scroll', handleScroll);

    // 监听窗口大小变化
    window.addEventListener('resize', function() {
      // 重新计算粘性容器的位置
      updateStickyPosition();

      // 更新占位符尺寸
      placeholders.forEach(function(item) {
        item.placeholder.style.width = item.element.offsetWidth + 'px';
        item.placeholder.style.height = item.element.offsetHeight + 'px';
      });
    });

    // 初始触发一次滚动事件
    handleScroll();
  }
});
